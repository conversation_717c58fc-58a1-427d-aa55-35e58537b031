apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  annotations:
    pipelinerun.cicd.work/branch: release/v28
    pipelinerun.cicd.work/buildNumber: "999"
    pipelinerun.cicd.work/csiVersion: v2
    pipelinerun.cicd.work/env: qa
    pipelinerun.cicd.work/pipelineId: "226"
    pipelinerun.cicd.work/repoAddress: https://gitlab.ttyuyin.com/harmony/cicd.git
    pipelinerun.cicd.work/requestDate: 2024-10
    volume.beta.kubernetes.io/storage-provisioner: 52tt.io/overlay-nfs-provisioner
    volume.kubernetes.io/storage-provisioner: 52tt.io/overlay-nfs-provisioner
  name: cicd-conf-pvc-qa-1866
  namespace: default
spec:
  accessModes:
  - Rea
  resources:
    requests:
      storage: 1Gi
  storageClassName: nfs-client
  volumeMode: Filesystem
