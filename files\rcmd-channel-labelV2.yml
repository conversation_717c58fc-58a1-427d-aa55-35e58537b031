apiVersion: apps/v1
kind: ReplicaSet
metadata:
  annotations:
    deployment.kubernetes.io/desired-replicas: '4'
    deployment.kubernetes.io/max-replicas: '6'
    deployment.kubernetes.io/revision: '73'
    deployment.kubernetes.io/revision-history: '71'
    meta.helm.sh/release-name: rcmd-channel-label
    meta.helm.sh/release-namespace: rcmd-tt
    mpe/flow-control-repo: rcmd
  creationTimestamp: '2024-02-22T20:19:46Z'
  generation: 12
  labels:
    app: rcmd-channel-label
    app.kubernetes.io/instance: rcmd-channel-label
    app.kubernetes.io/name: rcmd-channel-label
    app.oam.dev/component: rcmd-channel-label
    app.oam.dev/name: rcmd-channel-label
    cluster_id: id_8
    env: PRODUCTION
    lang: go
    pod-template-hash: 586564fbd8
    senv: base
    type: app
    uuid: 62f25129add92a4febe0a2bd
  name: rcmd-channel-label-586564fbd8
  namespace: rcmd-tt
  ownerReferences:
    - apiVersion: apps/v1
      blockOwnerDeletion: true
      controller: true
      kind: Deployment
      name: rcmd-channel-label
      uid: e6089068-509a-4cb2-9c96-dc60a095bcd1
  resourceVersion: '1953012548'
  uid: f0fa04a0-4d88-4e6a-a2d3-9b3a190cd8bd
spec:
  replicas: 4
  selector:
    matchLabels:
      app: rcmd-channel-label
      app.kubernetes.io/instance: rcmd-channel-label
      app.kubernetes.io/name: rcmd-channel-label
      pod-template-hash: 586564fbd8
  template:
    metadata:
      annotations:
        checksum/config: 6c6160fc5d8f8b670e6b81d3c902f8a12630eb0c4fec5b29b9c2754a277874e9
        kubectl.kubernetes.io/restartedAt: '2024-01-08T15:57:28+08:00'
        mpe/flow-control-repo: rcmd
        sidecar.istio.io/interceptionMode: REDIRECT
        sidecar.istio.io/proxyCPU: '0.2'
        sidecar.istio.io/proxyCPULimit: '2'
        sidecar.istio.io/proxyMemory: 256Mi
        sidecar.istio.io/proxyMemoryLimit: 1Gi
        telemetry.mesh.quwan.io/customMetricsContainer: ''
        telemetry.mesh.quwan.io/customMetricsPath: /metrics
        telemetry.mesh.quwan.io/customMetricsPort: '2112'
        telemetry.mesh.quwan.io/customMetricsScrape: 'true'
        traffic.sidecar.istio.io/excludeOutboundPorts: '26379,9221,6379,3306,9092,2181,2379,9080,9669'
      creationTimestamp: null
      labels:
        app: rcmd-channel-label
        app.kubernetes.io/instance: rcmd-channel-label
        app.kubernetes.io/name: rcmd-channel-label
        app.oam.dev/component: rcmd-channel-label
        app.oam.dev/name: rcmd-channel-label
        cluster_id: id_8
        env: PRODUCTION
        lang: go
        pod-template-hash: 586564fbd8
        senv: base
        type: app
        uuid: 62f25129add92a4febe0a2bd
    spec:
      containers:
        - command:
            - /app/rcmd-channel-label
            - '--config=/app/config/rcmd-channel-label.json'
            - '--with-name-registration=false'
            - '--listen=:8000'
            - '--log_level=info'
            - '--wait-sidecar-ready=true'
          env:
            - name: TZ
              value: Asia/Shanghai
            - name: MY_CLOUD_NAME
              value: HUAWEI
            - name: MY_APP_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: 'metadata.labels[''app.kubernetes.io/name'']'
            - name: MY_NODE_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: spec.nodeName
            - name: MY_POD_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.name
            - name: MY_POD_NAMESPACE
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.namespace
            - name: MY_POD_IP
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: status.podIP
            - name: MY_POD_SERVICE_ACCOUNT
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: spec.serviceAccountName
            - name: POD_CPU_REQUEST
              valueFrom:
                resourceFieldRef:
                  containerName: rcmd-channel-label
                  divisor: 1m
                  resource: requests.cpu
            - name: POD_CPU_LIMIT
              valueFrom:
                resourceFieldRef:
                  containerName: rcmd-channel-label
                  divisor: 1m
                  resource: limits.cpu
            - name: POD_MEM_REQUEST
              valueFrom:
                resourceFieldRef:
                  containerName: rcmd-channel-label
                  divisor: 1Mi
                  resource: requests.memory
            - name: POD_MEM_LIMIT
              valueFrom:
                resourceFieldRef:
                  containerName: rcmd-channel-label
                  divisor: 1Mi
                  resource: limits.memory
            - name: MY_IMAGE_VERSION
              value: V20240222174018-f1926acb5
            - name: TT_Sentinel_CD_AT
              value: '2024-02-22 17:46:05.113841'
            - name: AB_TEST_URL
              value: >-
                http://abtest.skyengine.net.cn/AbtestLogicService/GetUsersAbtestByTag
            - name: AB_TEST_APPID
              value: '10007'
          image: >-
            cr.ttyuyin.com/zt/rcmd-channel-label:V20240125085111-release-channel_dynmic_num-5611160ea
          imagePullPolicy: IfNotPresent
          livenessProbe:
            exec:
              command:
                - grpc_health_probe
                - '-addr=127.0.0.1:8000'
                - '-rpc-timeout=2s'
            failureThreshold: 3
            initialDelaySeconds: 30
            periodSeconds: 5
            successThreshold: 1
            timeoutSeconds: 2
          name: rcmd-channel-label
          ports:
            - containerPort: 8000
              name: grpc-8000
              protocol: TCP
            - containerPort: 2112
              name: http-metrics
              protocol: TCP
          readinessProbe:
            exec:
              command:
                - grpc_health_probe
                - '-addr=127.0.0.1:8000'
                - '-rpc-timeout=2s'
            failureThreshold: 3
            initialDelaySeconds: 20
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 1
          resources:
            limits:
              cpu: '4'
              memory: 3Gi
            requests:
              cpu: '1'
              memory: 3Gi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
            - mountPath: /app/config
              name: config
            - mountPath: /root/etc/client/
              name: service-targets-config
            - mountPath: /home/<USER>/log/oss/osspipe_no_del
              name: osspipe-no-del
            - mountPath: /home/<USER>/log/oss/oss_bak_pipe_no_del
              name: oss-bak-pipe-no-del
            - mountPath: /etc/localtime
              name: host-time
              readOnly: true
            - mountPath: /data
              name: obs-model-manager
      dnsPolicy: ClusterFirst
      imagePullSecrets:
        - name: default-secret
      priorityClassName: prod-p1
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      serviceAccount: rcmd-reader
      serviceAccountName: rcmd-reader
      terminationGracePeriodSeconds: 30
      tolerations:
        - effect: NoExecute
          key: node.kubernetes.io/not-ready
          operator: Exists
          tolerationSeconds: 30
        - effect: NoExecute
          key: node.kubernetes.io/unreachable
          operator: Exists
          tolerationSeconds: 30
      topologySpreadConstraints:
        - labelSelector:
            matchLabels:
              app: rcmd-channel-label
          maxSkew: 1
          topologyKey: topology.kubernetes.io/zone
          whenUnsatisfiable: ScheduleAnyway
      volumes:
        - configMap:
            defaultMode: 420
            name: rcmd-channel-label
          name: config
        - configMap:
            defaultMode: 420
            name: service-targets-config
          name: service-targets-config
        - hostPath:
            path: /dev/null
            type: ''
          name: osspipe-no-del
        - hostPath:
            path: /dev/null
            type: ''
          name: oss-bak-pipe-no-del
        - hostPath:
            path: /etc/localtime
            type: ''
          name: host-time
        - name: obs-model-manager
          persistentVolumeClaim:
            claimName: hw-bj-zt-model-server
status:
  availableReplicas: 4
  fullyLabeledReplicas: 4
  observedGeneration: 12
  readyReplicas: 4
  replicas: 4
