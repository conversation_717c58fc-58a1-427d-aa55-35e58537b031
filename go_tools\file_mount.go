package main

import (
	"fmt"
	"os"
	"os/exec"
	"strings"

	"github.com/spf13/cobra"
	"golang.org/x/sys/unix"
)

var rootCmd = &cobra.Command{
	Use:   "kendo",
	Short: "KendoCross Tools",
	Long:  "3D Boys 有偿收费工具集合",
}

func init() {
	rootCmd.AddCommand(FileHelpCmd)
}
func main() {
	if err := rootCmd.Execute(); err != nil {
		fmt.Fprintln(os.Stderr, err)
		os.Exit(1)
	}
}

var FileHelpCmd = &cobra.Command{
	Use:     "file",
	Short:   "file Mount Unmout",
	Long:    `文件夹 的一些帮助 `,
	Version: "1.0.0",
	Run:     execute,
}

var mergedDir string
var mount bool
var unmout bool

func init() {
	FileHelpCmd.PersistentFlags().StringVarP(&mergedDir, "dir", "d", "", "Merged Dir")
	FileHelpCmd.PersistentFlags().BoolVarP(&mount, "mount", "m", false, "Mount Dir")
	FileHelpCmd.PersistentFlags().BoolVarP(&unmout, "unmout", "u", false, "UnMount Dir")
}

func execute(cmd *cobra.Command, args []string) {
	defer func() {
		if r := recover(); r != nil {
			fmt.Printf("execute Err:%v", r)
			return
		}
	}()
	err := cmdRun()
	if err != nil {
		fmt.Println(err.Error())
		return
	}
}

func cmdRun() (err error) {
	if mergedDir == "" {
		println("Must Input mergedDir")
		return
	}

	if unmout {
		err = Umount(mergedDir)
		if err != nil {
			println(err.Error())
			return
		}
		return
	}

	return

}

func Umount(mergedDir string) error {
	println("Umount " + mergedDir)

	println("unexport  ")
	cmd := exec.Command("/usr/sbin/exportfs", "-u", "*:"+mergedDir)
	stdout, err := cmd.Output()
	if err != nil {
		fmt.Printf("failed to unexport dir %s: %v, output: %v \n", mergedDir, err, stdout)
	}

	// Check if the directory has been mounted.
	cmd = exec.Command("/bin/mountpoint", "-q", mergedDir)
	err = cmd.Run()
	if err != nil {
		fmt.Printf("failed to check mountpoint %s: %v \n", mergedDir, err)
	}

	// Umount the overlay.
	println("unix.Unmount ")
	if err := unix.Unmount(mergedDir, 0); err != nil {
		return fmt.Errorf("failed to unmount overlay %s: %v", mergedDir, err)
	}

	cmd = exec.Command("rm", "-rf", mergedDir)
	if err := cmd.Run(); err == nil {
		return fmt.Errorf("failed to remove dir %s: %v", mergedDir, err)
	}

	return nil
}

func isExported(mergedDir string) bool {
	etabData, err := os.ReadFile("/var/lib/nfs/etab")
	if err != nil {
		return false
	}
	if strings.Contains(string(etabData), mergedDir) {
		return true
	}
	return false
}
