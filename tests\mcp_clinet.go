package main

import (
	"context"
	"fmt"
	"log"

	"github.com/mark3labs/mcp-go/client"
	"github.com/mark3labs/mcp-go/mcp"
)

// MCPClient 封装MCP客户端功能
type MCPClient struct {
	client *client.Client
}

// NewMCPClient 创建新的MCP客户端
func NewMCPClient() (*MCPClient, error) {
	// 创建MCP客户端连接 - 使用stdio transport
	mcpClient, err := client.NewSSEMCPClient("http://cicd-qa.ttyuyin.com/mcp/sse")
	if err != nil {
		return nil, fmt.Errorf("failed to create MCP client: %w", err)
	}

	return &MCPClient{
		client: mcpClient,
	}, nil
}

// ConnectAndListTools 连接到MCP服务端并打印所有工具名
func (c *MCPClient) ConnectAndListTools(ctx context.Context) error {
	// 初始化连接
	initRequest := mcp.InitializeRequest{
		Params: mcp.InitializeParams{
			ProtocolVersion: mcp.LATEST_PROTOCOL_VERSION,
			Capabilities:    mcp.ClientCapabilities{},
			ClientInfo: mcp.Implementation{
				Name:    "mcp-go-client",
				Version: "1.0.0",
			},
		},
	}

	_, err := c.client.Initialize(ctx, initRequest)
	if err != nil {
		return fmt.Errorf("failed to initialize MCP client: %w", err)
	}
	defer c.client.Close()

	fmt.Println("Successfully connected to MCP server")

	// 获取所有可用的工具
	toolsRequest := mcp.ListToolsRequest{}
	toolsResult, err := c.client.ListTools(ctx, toolsRequest)
	if err != nil {
		return fmt.Errorf("failed to list tools: %w", err)
	}

	// 打印工具名称
	fmt.Printf("Found %d tools:\n", len(toolsResult.Tools))
	for i, tool := range toolsResult.Tools {
		fmt.Printf("%d. %s\n", i+1, tool.Name)
		if tool.Description != "" {
			fmt.Printf("   Description: %s\n", tool.Description)
		}
		fmt.Println() // 添加空行以便阅读
	}

	return nil
}

func main() {

	// 创建MCP客户端
	client, err := NewMCPClient()
	if err != nil {
		log.Fatalf("Failed to create MCP client: %v", err)
	}

	// 创建上下文
	ctx := context.Background()

	// 连接并列出工具
	if err := client.ConnectAndListTools(ctx); err != nil {
		log.Fatalf("Failed to connect and list tools: %v", err)
	}
}
