apiVersion: apps/v1
kind: Deployment
metadata:
  annotations: {}
  name: cia-test-001
spec:
  replicas: 1
  selector:
    matchLabels:
      app: cia-test-001
  template:
    metadata:
      labels:
        app: cia-test-001
    spec:
      containers:
      - image: cr.ttyuyin.com/devops/43/rhythm-mic-name:V20250821144647-0e47eaa43c
        name: cia-test-001
        # command: [ "/bin/bash", "-c", "--" ]
        command: [ "sh", "-c", "tail -f /dev/null" ]
        # args: [ "sleep infinity" ]
        ports:
        - containerPort: 8080
          name: http
          protocol: TCP
        securityContext:
          privileged: true
        resources:
          limits:
            cpu: 1
            memory: 1024Mi
          requests:
            cpu: "0.2"
            memory: 128Mi