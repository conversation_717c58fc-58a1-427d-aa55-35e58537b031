{
    "Version"   = "1",
    "Statement" = [
      {
        "Effect"    = "Allow",
        "Principal" = [alicloud_ram_user.user.id],
        "Action"    = [
          "oss:RestoreObjectVersion",
          "oss:RestoreObject",
          "oss:PutObjectVersionTagging",
          "oss:PutObjectVersionAcl",
          "oss:PutObjectTagging",
          "oss:PutObject",
          "oss:PutObjectAcl",
          "oss:PutLiveChannelStatus",
          "oss:PutLiveChannel",
          "oss:PutBucketWebsite",
          "oss:PutBucketVersioning",
          "oss:PutBucketTagging",
          "oss:PutBucketRequestPayment",
          "oss:PutBucketReplication",
          "oss:PutBucketReferer",
          "oss:PutBucketPolicy",
          "oss:PutBucketLogging",
          "oss:PutBucketLifecycle",
          "oss:PutBucket",
          "oss:PutBucketEncryption",
          "oss:PutBucketCors",
          "oss:PutBucketAcl",
          "oss:ProcessImm",
          "oss:PostVodPlaylist",
          "oss:PostProcessTask",
          "oss:ListParts",
          "oss:ListObjectVersions",
          "oss:ListObjects",
          "oss:ListMultipartUploads",
          "oss:ListLiveChannel",
          "oss:GetVodPlaylist",
          "oss:GetObjectVersionTagging",
          "oss:GetObjectVersion",
          "oss:GetObjectVersionAcl",
          "oss:GetObjectTagging",
          "oss:GetObject",
          "oss:GetObjectAcl",
          "oss:GetLiveChannelStat",
          "oss:GetLiveChannelHistory",
          "oss:GetLiveChannel",
          "oss:GetBucketWebsite",
          "oss:GetBucketVersioning",
          "oss:GetBucketTagging",
          "oss:GetBucketRequestPayment",
          "oss:GetBucketReplicationPro",
          "oss:GetBucketReplicationLoc",
          "oss:GetBucketReplication",
          "oss:GetBucketReferer",
          "oss:GetBucketPolicy",
          "oss:GetBucketLogging",
          "oss:GetBucketLocation",
          "oss:GetBucketLifecycle",
          "oss:GetBucketInfo",
          "oss:GetBucketEncryption",
          "oss:GetBucketCors",
          "oss:GetBucketAcl",
          "oss:AbortMultipartUpload"
        ],
        "Resource" = ["acs:oss:*:${var.domain_id}:${alicloud_oss_bucket.oss.bucket}/*"]
      }
    ]
  }