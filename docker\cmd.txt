


CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build 

docker build -t  cr.ttyuyin.com/devops/tekton/git-init:v0.40.11 .
docker build -t  cr.ttyuyin.com/devops/tekton/git-init:v0.40.13 .
docker build -t  cr.ttyuyin.com/devops/tekton/git-init:v0.50.0 .
docker build -t  cr.ttyuyin.com/devops/tekton/git-init:v0.51.1 .

docker build -t  cr.ttyuyin.com/devops/tekton/maven3.6:v0.1 .

docker build -t cr.ttyuyin.com/devops/cicd-csi-cleaner:v0.8 .