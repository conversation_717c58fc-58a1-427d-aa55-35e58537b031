package main

import (
	"fmt"

	"github.com/apolloconfig/agollo/v4"
	"github.com/apolloconfig/agollo/v4/env/config"
)

func main() {
	namespace := "tt-cicd-config-30.txt"
	apolloSvc := "http://apolloconfig.svc.quwan.local"
	// apolloSvc := "http://************:8080"
	c := &config.AppConfig{
		AppID:             "tt-cicd-qa",
		Cluster:           "default",
		IP:                apolloSvc,
		NamespaceName:     namespace,
		IsBackupConfig:    true,
		Secret:            "a2ce0a922cd34466b4af6b720a5854b5",
		Label:             "appName",
		MustStart:         true,
		SyncServerTimeout: 10,
	}

	client, err := agollo.StartWithConfig(func() (*config.AppConfig, error) {
		return c, nil
	})
	if err != nil {
		fmt.Printf("初始化Apollo配置失败 %v \n", err)
		return
	}
	fmt.Println("初始化Apollo配置成功")

	// listener := &nodeCfgListener{}

	// client.AddChangeListener(listener)

	//Use your apollo key to test
	cfg := client.GetConfig(namespace)
	// 等待配置获取完成
	ct := cfg.GetStringValue("content", "")
	fmt.Println(ct)
}
