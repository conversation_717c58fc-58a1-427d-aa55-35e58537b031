apiVersion: apps/v1
kind: Deployment
metadata:
  annotations: {}
  name: a-config-check
spec:
  replicas: 1
  selector:
    matchLabels:
      app: a-config-check
  template:
    metadata:
      annotations:
        com.ttyuyin.cicd.csi/pod.cfg.info: |-
          {
            "appName": "cheny-ser",
            "env": 1,
            "envTag": 1,
            "subEnv":"cannay",
            "dyCfgs":[
              {
                "apolloNS":"txt001.txt",
                "cfgFile":"/cicd/config.yaml",
                "isGlobal":true
              },
              {
                "apolloNS":"cia.txt",
                "cfgFile":"/cicd/cia.yaml"
              }
            ]
          }
      labels:
        app: a-config-check
    spec:
      nodeSelector:
        kubernetes.io/hostname: ************
      volumes:
      - name: pkgcache
        persistentVolumeClaim:
          claimName: cicd-conf-pvc-qa-1210
      # - hostPath:
      #     path: /data/tt-cicd-csi/
      #     type: DirectoryOrCreate
      #   name: mnt
      containers:
      # - image: cr.ttyuyin.com/devops/ubuntu:22.04
      - image: cr.ttyuyin.com/develop-tools/nicolaka/netshoot
        name: a-config-check
        command: [ "/bin/bash", "-c", "--" ]
        args: [ "sleep infinity" ]
        volumeMounts:
          - name: pkgcache
            mountPath: /data/cicd-dy-conf/
            
          # - name: mnt
          #   mountPath: /data/tt-cicd-csi/
        ports:
        - containerPort: 8080
          name: http
          protocol: TCP
        securityContext:
          privileged: true
        resources:
          limits:
            cpu: 1
            memory: 1024Mi
          requests:
            cpu: "0.2"
            memory: 128Mi