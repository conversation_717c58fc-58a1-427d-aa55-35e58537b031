{"Version": "1", "Statement": [{"Effect": "Allow", "Action": ["oss:RestoreObject", "oss:PutObjectVersionTagging", "oss:PutObjectVersionAcl", "oss:PutObjectTagging", "oss:PutObject", "oss:PutObjectAcl", "oss:PutLiveChannelStatus", "oss:PutLiveChannel", "oss:PutBucketWebsite", "oss:PutBucketVersioning", "oss:PutBucketTagging", "oss:PutBucketRequestPayment", "oss:PutBucketReplication", "oss:<PERSON><PERSON><PERSON>etReferer", "oss:PutBucketPolicy", "oss:Put<PERSON>ucketLogging", "oss:PutBucketLifecycle", "oss:PutBucket", "oss:PutBucketEncryption", "oss:PutBucketCors", "oss:PutBucketAcl", "oss:ProcessImm", "oss:PostVodPlaylist", "oss:PostProcessTask", "oss:ListParts", "oss:ListObjectVersions", "oss:ListObjects", "oss:ListMultipartUploads", "oss:ListLiveChannel", "oss:GetVodPlaylist", "oss:GetObjectVersionTagging", "oss:GetObjectVersion", "oss:GetObjectVersionAcl", "oss:GetObjectTagging", "oss:GetObject", "oss:GetObjectAcl", "oss:GetLiveChannelStat", "oss:GetLiveChannelHistory", "oss:GetLiveChannel", "oss:GetBucketWebsite", "oss:GetBucketVersioning", "oss:GetBucketTagging", "oss:GetBucketRequestPayment", "oss:GetBucketReplication", "oss:GetBucketReferer", "oss:GetBucketPolicy", "oss:GetBucketLogging", "oss:GetBucketLocation", "oss:GetBucketLifecycle", "oss:GetBucketInfo", "oss:GetBucketEncryption", "oss:GetBucketCors", "oss:GetBucketAcl", "oss:AbortMultipartUpload"], "Principal": ["206499749553267436"], "Resource": ["acs:oss:*:1538072316762279:oss-prod-ali-bj-avl-training/*"]}, {"Effect": "Allow", "Action": ["oss:ListObjects", "oss:GetObject"], "Principal": ["206499749553267436"], "Resource": ["acs:oss:*:1538072316762279:oss-prod-ali-bj-avl-training"], "Condition": {"StringLike": {"oss:Prefix": ["*"]}}}]}