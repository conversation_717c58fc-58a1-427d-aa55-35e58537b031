跳转到主要内容
default

qa-ci-pipelinerun-3805
最后更新于 11分钟前
Running
Tasks Completed: 1 (Failed: 0, Cancelled 0), Incomplete: 2, Skipped: 0
将状态消息复制到剪贴板
git-clone-stage-827-task-1105
golang-build-stage-827-task-1106
build
kaniko-stage-828-task-1107
qa-ci-pipelinerun-3805-golang-build-stage-827-task-1106
Running
参数
状态
Pod

资源

事件
kind: Pod
apiVersion: v1
metadata:
  name: qa-ci-pipelinerun-3805-golang-build-stage-827-task-1106-pod
  namespace: default
  uid: 428128cd-a718-48cf-9767-9aa9f4e80ea4
  resourceVersion: '170918385'
  creationTimestamp: '2023-09-15T11:27:16Z'
  labels:
    app.kubernetes.io/instance: tekton
    app.kubernetes.io/managed-by: tekton-pipelines
    app.kubernetes.io/version: '0.3'
    tekton.dev/memberOf: tasks
    tekton.dev/pipeline: 55e300b7-6b27-47ea-87ee-a4646623a604
    tekton.dev/pipelineRun: qa-ci-pipelinerun-3805
    tekton.dev/pipelineTask: golang-build-stage-827-task-1106
    tekton.dev/task: golang-build
    tekton.dev/taskRun: qa-ci-pipelinerun-3805-golang-build-stage-827-task-1106
  annotations:
    pipeline.tekton.dev/release: 086e76a
    pipelinerun.cicd.work/pipelineRunId: '3805'
    pipelinerun.cicd.work/pipelineRunStageId: '21587'
    pipelinerun.cicd.work/pipelineRunTaskId: '28838'
    pipelinerun.cicd.work/projectId: '1'
    pipelinerun.cicd.work/templateId: '160'
    tekton.dev/categories: Build Tools
    tekton.dev/displayName: golang build
    tekton.dev/pipelines.minVersion: 0.12.1
    tekton.dev/platforms: linux/amd64,linux/s390x,linux/ppc64le
    tekton.dev/ready: READY
    tekton.dev/tags: build-tool
    tke.cloud.tencent.com/networks-status: |
      [{
          "name": "tke-route-eni",
          "interface": "eth0",
          "ips": [
              "*************"
          ],
          "mac": "d6:8e:82:89:2c:83",
          "default": true,
          "dns": {}
      }]
  ownerReferences:
    - apiVersion: tekton.dev/v1beta1
      kind: TaskRun
      name: qa-ci-pipelinerun-3805-golang-build-stage-827-task-1106
      uid: e0cf2cbf-8db5-464d-a804-827e77f4b998
      controller: true
      blockOwnerDeletion: true
spec:
  volumes:
    - name: tekton-internal-workspace
      emptyDir: {}
    - name: tekton-internal-home
      emptyDir: {}
    - name: tekton-internal-results
      emptyDir: {}
    - name: tekton-internal-steps
      emptyDir: {}
    - name: tekton-internal-scripts
      emptyDir: {}
    - name: tekton-internal-bin
      emptyDir: {}
    - name: tekton-internal-downward
      downwardAPI:
        items:
          - path: ready
            fieldRef:
              apiVersion: v1
              fieldPath: metadata.annotations['tekton.dev/ready']
        defaultMode: 420
    - name: tekton-creds-init-home-0
      emptyDir:
        medium: Memory
    - name: tekton-internal-run-0
      emptyDir: {}
    - name: ws-k4pnd
      persistentVolumeClaim:
        claimName: pvc-0c89bf7503
    - name: ws-5vnrn
      persistentVolumeClaim:
        claimName: pvc-nfs
    - name: kube-api-access-z8c86
      projected:
        sources:
          - serviceAccountToken:
              expirationSeconds: 3607
              path: token
          - configMap:
              name: kube-root-ca.crt
              items:
                - key: ca.crt
                  path: ca.crt
          - downwardAPI:
              items:
                - path: namespace
                  fieldRef:
                    apiVersion: v1
                    fieldPath: metadata.namespace
        defaultMode: 420
  initContainers:
    - name: prepare
      image: alexezio.azurecr.io/tekton-releases/github.com/tektoncd/pipeline/cmd/entrypoint:v0.46.0
      command:
        - /ko-app/entrypoint
        - init
        - /ko-app/entrypoint
        - /tekton/bin/entrypoint
        - step-build
      workingDir: /
      resources: {}
      volumeMounts:
        - name: tekton-internal-bin
          mountPath: /tekton/bin
        - name: tekton-internal-steps
          mountPath: /tekton/steps
        - name: kube-api-access-z8c86
          readOnly: true
          mountPath: /var/run/secrets/kubernetes.io/serviceaccount
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      imagePullPolicy: IfNotPresent
    - name: place-scripts
      image: cr.ttyuyin.com/devops/busybox:latest
      command:
        - sh
      args:
        - '-c'
        - |
          scriptfile="/tekton/scripts/script-0-t4jx7"
          touch ${scriptfile} && chmod +x ${scriptfile}
          cat > ${scriptfile} << '_EOF_'
          IyEvYmluL3NoCnNldCAtZQojIGlmIFsgISAtZSAkR09QQVRIL3NyYy8vZ28ubW9kIF07dGhlbgojICAgU1JDX1BBVEg9IiRHT1BBVEgvc3JjLyIKIyAgIG1rZGlyIC1wICRTUkNfUEFUSAojICAgY3AgLVIgIi93b3Jrc3BhY2Uvc291cmNlIi8qICRTUkNfUEFUSAojICAgY2QgJFNSQ19QQVRICiMgZmkKIyBnbyBidWlsZCAtdiAuL2NtZC8uLi4KZWNobyAi5omT5Y2wZ2/niYjmnKzvvJoiCmdvIHZlcnNpb24KZXhwb3J0IEdPMTExTU9EVUxFPW9uCmV4cG9ydCBHT1BST1hZPWh0dHA6Ly95dy1uZXh1cy50dHl1eWluLmNvbTo4MDgxL3JlcG9zaXRvcnkvZ3JvdXAtZ28vCmV4cG9ydCBHT1NVTURCPW9mZgpleHBvcnQgR09GTEFHUz0iLWJ1aWxkdmNzPWZhbHNlIgpzbGVlcCBpbmZpbml0eQpjZCBzZXJ2aWNlcy91c2VyLW9ubGluZSAmJiBDR09fRU5BQkxFRD0wIEdPT1M9bGludXggR09BUkNIPWFtZDY0IGdvIGJ1aWxkIC1idWlsZHZjcz1mYWxzZSAtbyB1c2VyLW9ubGluZSAuLwplY2hvICLnvJbor5HlrozmiJDvvIwg5p+l55yL55Sf5oiQ55qE5LqM6L+b5Yi25paH5Lu277yaIgpscyAtbAo=
          _EOF_
          /tekton/bin/entrypoint decode-script "${scriptfile}"
          
      resources: {}
      volumeMounts:
        - name: tekton-internal-scripts
          mountPath: /tekton/scripts
        - name: tekton-internal-bin
          mountPath: /tekton/bin
        - name: kube-api-access-z8c86
          readOnly: true
          mountPath: /var/run/secrets/kubernetes.io/serviceaccount
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      imagePullPolicy: Always
    - name: working-dir-initializer
      image: alexezio.azurecr.io/tekton-releases/github.com/tektoncd/pipeline/cmd/workingdirinit:v0.46.0
      command:
        - /ko-app/workingdirinit
      args:
        - /workspace/source
      workingDir: /workspace
      resources: {}
      volumeMounts:
        - name: tekton-internal-workspace
          mountPath: /workspace
        - name: tekton-internal-home
          mountPath: /tekton/home
        - name: tekton-internal-results
          mountPath: /tekton/results
        - name: tekton-internal-steps
          readOnly: true
          mountPath: /tekton/steps
        - name: kube-api-access-z8c86
          readOnly: true
          mountPath: /var/run/secrets/kubernetes.io/serviceaccount
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      imagePullPolicy: IfNotPresent
  containers:
    - name: step-build
      image: docker.io/library/golang:latest
      command:
        - /tekton/bin/entrypoint
      args:
        - '-wait_file'
        - /tekton/downward/ready
        - '-wait_file_content'
        - '-post_file'
        - /tekton/run/0/out
        - '-termination_path'
        - /tekton/termination
        - '-step_metadata_dir'
        - /tekton/run/0/status
        - '-entrypoint'
        - /tekton/scripts/script-0-t4jx7
        - '--'
      workingDir: /workspace/source
      env:
        - name: GOOS
          value: linux
        - name: GOARCH
          value: amd64
        - name: GO111MODULE
          value: auto
        - name: GOCACHE
          value: /root/.cache/go-build
        - name: GOMODCACHE
          value: /go/pkg/mod
        - name: CGO_ENABLED
        - name: GOSUMDB
        - name: GOPROXY
          value: http://yw-nexus.ttyuyin.com:8081/repository/group-go/
        - name: GONOPROXY
          value: gitlab.52tt.com
      resources:
        limits:
          tke.cloud.tencent.com/eni-ip: '1'
        requests:
          tke.cloud.tencent.com/eni-ip: '1'
      volumeMounts:
        - name: ws-k4pnd
          mountPath: /workspace/source
        - name: ws-5vnrn
          mountPath: /go/pkg/mod
          subPath: cache/pkg/user-online
        - name: ws-5vnrn
          mountPath: /root/.cache/go-build
          subPath: cache/build/user-online
        - name: tekton-internal-scripts
          readOnly: true
          mountPath: /tekton/scripts
        - name: tekton-internal-downward
          readOnly: true
          mountPath: /tekton/downward
        - name: tekton-creds-init-home-0
          mountPath: /tekton/creds
        - name: tekton-internal-run-0
          mountPath: /tekton/run/0
        - name: tekton-internal-bin
          readOnly: true
          mountPath: /tekton/bin
        - name: tekton-internal-workspace
          mountPath: /workspace
        - name: tekton-internal-home
          mountPath: /tekton/home
        - name: tekton-internal-results
          mountPath: /tekton/results
        - name: tekton-internal-steps
          readOnly: true
          mountPath: /tekton/steps
        - name: kube-api-access-z8c86
          readOnly: true
          mountPath: /var/run/secrets/kubernetes.io/serviceaccount
      terminationMessagePath: /tekton/termination
      terminationMessagePolicy: File
      imagePullPolicy: Always
  restartPolicy: Never
  terminationGracePeriodSeconds: 30
  activeDeadlineSeconds: 5400
  dnsPolicy: ClusterFirst
  serviceAccountName: default
  serviceAccount: default
  nodeName: ************
  securityContext: {}
  imagePullSecrets:
    - name: registry
  schedulerName: default-scheduler
  tolerations:
    - key: node.kubernetes.io/not-ready
      operator: Exists
      effect: NoExecute
      tolerationSeconds: 300
    - key: node.kubernetes.io/unreachable
      operator: Exists
      effect: NoExecute
      tolerationSeconds: 300
  priorityClassName: prod-p4
  priority: 1600
  enableServiceLinks: true
  preemptionPolicy: Never
status:
  phase: Running
  conditions:
    - type: Initialized
      status: 'True'
      lastProbeTime: null
      lastTransitionTime: '2023-09-15T11:27:20Z'
    - type: Ready
      status: 'True'
      lastProbeTime: null
      lastTransitionTime: '2023-09-15T11:27:21Z'
    - type: ContainersReady
      status: 'True'
      lastProbeTime: null
      lastTransitionTime: '2023-09-15T11:27:21Z'
    - type: PodScheduled
      status: 'True'
      lastProbeTime: null
      lastTransitionTime: '2023-09-15T11:27:17Z'
  hostIP: ************
  podIP: *************
  podIPs:
    - ip: *************
  startTime: '2023-09-15T11:27:17Z'
  initContainerStatuses:
    - name: prepare
      state:
        terminated:
          exitCode: 0
          reason: Completed
          startedAt: '2023-09-15T11:27:17Z'
          finishedAt: '2023-09-15T11:27:18Z'
          containerID: containerd://2b7ccdda8c662e57c9a39549a3103681f880ef27c2a4e1f6dc12c8633eae6c0d
      lastState: {}
      ready: true
      restartCount: 0
      image: alexezio.azurecr.io/tekton-releases/github.com/tektoncd/pipeline/cmd/entrypoint:v0.46.0
      imageID: alexezio.azurecr.io/tekton-releases/github.com/tektoncd/pipeline/cmd/entrypoint@sha256:0a4e875cb42ec35dfa34f46d99bd5e7b446c45490af7fe2b0733d39c13b6a1d5
      containerID: containerd://2b7ccdda8c662e57c9a39549a3103681f880ef27c2a4e1f6dc12c8633eae6c0d
    - name: place-scripts
      state:
        terminated:
          exitCode: 0
          reason: Completed
          startedAt: '2023-09-15T11:27:18Z'
          finishedAt: '2023-09-15T11:27:18Z'
          containerID: containerd://3801ade3e230098cb3c8a6ef81a68384646df48dfef693cd17a95c82223e4a3b
      lastState: {}
      ready: true
      restartCount: 0
      image: cgr.dev/chainguard/busybox:latest
      imageID: cgr.dev/chainguard/busybox@sha256:49ed88d910408d547ceb021dd7582130108159bb0ddd6dec9da8a3dbf4b8db33
      containerID: containerd://3801ade3e230098cb3c8a6ef81a68384646df48dfef693cd17a95c82223e4a3b
    - name: working-dir-initializer
      state:
        terminated:
          exitCode: 0
          reason: Completed
          startedAt: '2023-09-15T11:27:19Z'
          finishedAt: '2023-09-15T11:27:19Z'
          containerID: containerd://a2c0516d5cc2f79f9763d3c45aa053c49712fa6023a541c10dd9faec0f99ffc2
      lastState: {}
      ready: true
      restartCount: 0
      image: alexezio.azurecr.io/tekton-releases/github.com/tektoncd/pipeline/cmd/workingdirinit:v0.46.0
      imageID: alexezio.azurecr.io/tekton-releases/github.com/tektoncd/pipeline/cmd/workingdirinit@sha256:070897ebfac7f143585808ef0b8c7488d61bbce7d9c77b93e8830af4147925b5
      containerID: containerd://a2c0516d5cc2f79f9763d3c45aa053c49712fa6023a541c10dd9faec0f99ffc2
  containerStatuses:
    - name: step-build
      state:
        running:
          startedAt: '2023-09-15T11:27:21Z'
      lastState: {}
      ready: true
      restartCount: 0
      image: docker.io/library/golang:latest
      imageID: docker.io/library/golang@sha256:62e5883c278354041360e26dd3ba8c62c8621743a2b4fd7f0f0caf00af45d15c
      containerID: containerd://7fc240f6d6aa7a5dc28fc992e186c6ba76490dab2fad5349e8cf631d0bc0ed32
      started: true
  qosClass: BestEffort
翻译