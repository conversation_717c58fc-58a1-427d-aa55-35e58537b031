package main

import (
	"fmt"
	"io"
	"log"
	"net/http"
)

func handleRequest(w http.ResponseWriter, r *http.Request) {
	// 获取请求方法
	method := r.Method
	// 获取请求URI
	uri := r.RequestURI

	// 读取请求体（对于POST、PUT等请求）
	body, err := io.ReadAll(r.Body)
	if err != nil {
		http.Error(w, "Error reading request body", http.StatusBadRequest)
		return
	}
	defer r.Body.Close()

	// 构建响应内容
	response := fmt.Sprintf("Method: %s\nURI: %s\n Body: %s", method, uri, body)

	log.Println(response)

	// 返回响应
	w.Header().Set("Content-Type", "text/plain")
	w.Write<PERSON>eader(http.StatusOK)
	w.Write([]byte(response))
}

func main() {
	http.HandleFunc("/", handleRequest)

	log.Println("Server is running on :8008...")
	log.Fatal(http.ListenAndServe(":8008", nil))
}
