跳转到主要内容
default

prod-ci-pipelinerun-6735
最后更新于 2分钟前
Failed
Tasks Completed: 2 (Failed: 1, Cancelled 0), Skipped: 4
将状态消息复制到剪贴板
git-clone-stage-139-task-173
golang-build-stage-139-task-174
build
golang-test-stage-140-task-175
go-check-style-stage-141-task-345
sonarqube-scanner-stage-141-task-176
kaniko-stage-142-task-177
prod-ci-pipelinerun-6735-golang-build-stage-139-task-174
Failed
持续时间：11秒
参数
状态
Pod

资源

事件
kind: Pod
apiVersion: v1
metadata:
  name: prod-ci-pipelinerun-6735-golang-build-stage-139-task-174-pod
  namespace: default
  uid: e1860bcf-54ae-4959-af43-2bc6e93473d7
  resourceVersion: '30786538461'
  creationTimestamp: '2023-09-15T11:34:01Z'
  labels:
    app.kubernetes.io/instance: tekton
    app.kubernetes.io/managed-by: tekton-pipelines
    app.kubernetes.io/version: '0.3'
    tekton.dev/memberOf: tasks
    tekton.dev/pipeline: e6234fd6-f2b3-4d6c-b243-85a9ccd2209c
    tekton.dev/pipelineRun: prod-ci-pipelinerun-6735
    tekton.dev/pipelineTask: golang-build-stage-139-task-174
    tekton.dev/task: golang-build
    tekton.dev/taskRun: prod-ci-pipelinerun-6735-golang-build-stage-139-task-174
  annotations:
    pipeline.tekton.dev/release: 086e76a
    pipelinerun.cicd.work/pipelineRunId: '6735'
    pipelinerun.cicd.work/pipelineRunStageId: '40840'
    pipelinerun.cicd.work/pipelineRunTaskId: '52142'
    pipelinerun.cicd.work/projectId: '7'
    pipelinerun.cicd.work/templateId: '27'
    tekton.dev/categories: Build Tools
    tekton.dev/displayName: golang build
    tekton.dev/pipelines.minVersion: 0.12.1
    tekton.dev/platforms: linux/amd64,linux/s390x,linux/ppc64le
    tekton.dev/ready: READY
    tekton.dev/tags: build-tool
    tke.cloud.tencent.com/networks-status: |
      [{
          "name": "tke-route-eni",
          "interface": "eth0",
          "ips": [
              "***********"
          ],
          "mac": "e6:03:9e:3e:c2:bb",
          "default": true,
          "dns": {}
      }]
  ownerReferences:
    - apiVersion: tekton.dev/v1beta1
      kind: TaskRun
      name: prod-ci-pipelinerun-6735-golang-build-stage-139-task-174
      uid: c315389a-2dcb-4905-be1c-2c1de644ad90
      controller: true
      blockOwnerDeletion: true
spec:
  volumes:
    - name: tekton-internal-workspace
      emptyDir: {}
    - name: tekton-internal-home
      emptyDir: {}
    - name: tekton-internal-results
      emptyDir: {}
    - name: tekton-internal-steps
      emptyDir: {}
    - name: tekton-internal-scripts
      emptyDir: {}
    - name: tekton-internal-bin
      emptyDir: {}
    - name: tekton-internal-downward
      downwardAPI:
        items:
          - path: ready
            fieldRef:
              apiVersion: v1
              fieldPath: metadata.annotations['tekton.dev/ready']
        defaultMode: 420
    - name: tekton-creds-init-home-0
      emptyDir:
        medium: Memory
    - name: tekton-internal-run-0
      emptyDir: {}
    - name: ws-fks5c
      persistentVolumeClaim:
        claimName: pvc-99c30f8ad0
    - name: ws-nhs9p
      persistentVolumeClaim:
        claimName: pvc-nfs
    - name: kube-api-access-w2b8g
      projected:
        sources:
          - serviceAccountToken:
              expirationSeconds: 3607
              path: token
          - configMap:
              name: kube-root-ca.crt
              items:
                - key: ca.crt
                  path: ca.crt
          - downwardAPI:
              items:
                - path: namespace
                  fieldRef:
                    apiVersion: v1
                    fieldPath: metadata.namespace
        defaultMode: 420
  initContainers:
    - name: prepare
      image: cr.ttyuyin.com/devops/tekton/entrypoint:v0.46.0
      command:
        - /ko-app/entrypoint
        - init
        - /ko-app/entrypoint
        - /tekton/bin/entrypoint
        - step-build
      workingDir: /
      resources: {}
      volumeMounts:
        - name: tekton-internal-bin
          mountPath: /tekton/bin
        - name: tekton-internal-steps
          mountPath: /tekton/steps
        - name: kube-api-access-w2b8g
          readOnly: true
          mountPath: /var/run/secrets/kubernetes.io/serviceaccount
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      imagePullPolicy: IfNotPresent
    - name: place-scripts
      image: cr.ttyuyin.com/devops/busybox:latest
      command:
        - sh
      args:
        - '-c'
        - |
          scriptfile="/tekton/scripts/script-0-jcfct"
          touch ${scriptfile} && chmod +x ${scriptfile}
          cat > ${scriptfile} << '_EOF_'
          IyEvYmluL3NoCnNldCAtZQojIGlmIFsgISAtZSAkR09QQVRIL3NyYy8vZ28ubW9kIF07dGhlbgojICAgU1JDX1BBVEg9IiRHT1BBVEgvc3JjLyIKIyAgIG1rZGlyIC1wICRTUkNfUEFUSAojICAgY3AgLVIgIi93b3Jrc3BhY2Uvc291cmNlIi8qICRTUkNfUEFUSAojICAgY2QgJFNSQ19QQVRICiMgZmkKIyBnbyBidWlsZCAtdiAuL2NtZC8uLi4KZWNobyAi5omT5Y2wZ2/niYjmnKzvvJoiCmdvIHZlcnNpb24KZXhwb3J0IEdPMTExTU9EVUxFPW9uCmV4cG9ydCBHT1BST1hZPWh0dHA6Ly95dy1uZXh1cy50dHl1eWluLmNvbTo4MDgxL3JlcG9zaXRvcnkvZ3JvdXAtZ28vCmV4cG9ydCBHT1NVTURCPW9mZgpleHBvcnQgR09GTEFHUz0iLWJ1aWxkdmNzPWZhbHNlIgpjZCBzZXJ2aWNlcy91c2VyLW9ubGluZSAmJiBDR09fRU5BQkxFRD0wIEdPT1M9bGludXggR09BUkNIPWFtZDY0IGdvIGJ1aWxkIC1idWlsZHZjcz1mYWxzZSAtbyB1c2VyLW9ubGluZSAuLwplY2hvICLnvJbor5HlrozmiJDvvIwg5p+l55yL55Sf5oiQ55qE5LqM6L+b5Yi25paH5Lu277yaIgpscyAtbAo=
          _EOF_
          /tekton/bin/entrypoint decode-script "${scriptfile}"
          
      resources: {}
      volumeMounts:
        - name: tekton-internal-scripts
          mountPath: /tekton/scripts
        - name: tekton-internal-bin
          mountPath: /tekton/bin
        - name: kube-api-access-w2b8g
          readOnly: true
          mountPath: /var/run/secrets/kubernetes.io/serviceaccount
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      imagePullPolicy: Always
    - name: working-dir-initializer
      image: cr.ttyuyin.com/devops/tekton/workingdirinit:v0.46.0
      command:
        - /ko-app/workingdirinit
      args:
        - /workspace/source
      workingDir: /workspace
      resources: {}
      volumeMounts:
        - name: tekton-internal-workspace
          mountPath: /workspace
        - name: tekton-internal-home
          mountPath: /tekton/home
        - name: tekton-internal-results
          mountPath: /tekton/results
        - name: tekton-internal-steps
          readOnly: true
          mountPath: /tekton/steps
        - name: kube-api-access-w2b8g
          readOnly: true
          mountPath: /var/run/secrets/kubernetes.io/serviceaccount
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      imagePullPolicy: IfNotPresent
  containers:
    - name: step-build
      image: docker.io/library/golang:latest
      command:
        - /tekton/bin/entrypoint
      args:
        - '-wait_file'
        - /tekton/downward/ready
        - '-wait_file_content'
        - '-post_file'
        - /tekton/run/0/out
        - '-termination_path'
        - /tekton/termination
        - '-step_metadata_dir'
        - /tekton/run/0/status
        - '-entrypoint'
        - /tekton/scripts/script-0-jcfct
        - '--'
      workingDir: /workspace/source
      env:
        - name: GOOS
          value: linux
        - name: GOARCH
          value: amd64
        - name: GO111MODULE
          value: auto
        - name: GOCACHE
          value: /root/.cache/go-build
        - name: GOMODCACHE
          value: /go/pkg/mod
        - name: CGO_ENABLED
        - name: GOSUMDB
        - name: GOPROXY
          value: http://yw-nexus.ttyuyin.com:8081/repository/group-go/
        - name: GONOPROXY
          value: gitlab.52tt.com
      resources:
        limits:
          tke.cloud.tencent.com/eni-ip: '1'
        requests:
          tke.cloud.tencent.com/eni-ip: '1'
      volumeMounts:
        - name: ws-fks5c
          mountPath: /workspace/source
        - name: ws-nhs9p
          mountPath: /go/pkg/mod
          subPath: cache/pkg/user-online
        - name: ws-nhs9p
          mountPath: /root/.cache/go-build
          subPath: cache/build/user-online
        - name: tekton-internal-scripts
          readOnly: true
          mountPath: /tekton/scripts
        - name: tekton-internal-downward
          readOnly: true
          mountPath: /tekton/downward
        - name: tekton-creds-init-home-0
          mountPath: /tekton/creds
        - name: tekton-internal-run-0
          mountPath: /tekton/run/0
        - name: tekton-internal-bin
          readOnly: true
          mountPath: /tekton/bin
        - name: tekton-internal-workspace
          mountPath: /workspace
        - name: tekton-internal-home
          mountPath: /tekton/home
        - name: tekton-internal-results
          mountPath: /tekton/results
        - name: tekton-internal-steps
          readOnly: true
          mountPath: /tekton/steps
        - name: kube-api-access-w2b8g
          readOnly: true
          mountPath: /var/run/secrets/kubernetes.io/serviceaccount
      terminationMessagePath: /tekton/termination
      terminationMessagePolicy: File
      imagePullPolicy: Always
  restartPolicy: Never
  terminationGracePeriodSeconds: 30
  activeDeadlineSeconds: 5400
  dnsPolicy: ClusterFirst
  serviceAccountName: default
  serviceAccount: default
  nodeName: ************
  securityContext: {}
  schedulerName: default-scheduler
  tolerations:
    - key: node.kubernetes.io/not-ready
      operator: Exists
      effect: NoExecute
      tolerationSeconds: 300
    - key: node.kubernetes.io/unreachable
      operator: Exists
      effect: NoExecute
      tolerationSeconds: 300
  priorityClassName: prod-p4
  priority: 1600
  enableServiceLinks: true
  preemptionPolicy: Never
status:
  phase: Failed
  conditions:
    - type: Initialized
      status: 'True'
      lastProbeTime: null
      lastTransitionTime: '2023-09-15T11:34:08Z'
    - type: Ready
      status: 'False'
      lastProbeTime: null
      lastTransitionTime: '2023-09-15T11:34:12Z'
      reason: PodFailed
    - type: ContainersReady
      status: 'False'
      lastProbeTime: null
      lastTransitionTime: '2023-09-15T11:34:12Z'
      reason: PodFailed
    - type: PodScheduled
      status: 'True'
      lastProbeTime: null
      lastTransitionTime: '2023-09-15T11:34:01Z'
  hostIP: ************
  podIP: ***********
  podIPs:
    - ip: ***********
  startTime: '2023-09-15T11:34:01Z'
  initContainerStatuses:
    - name: prepare
      state:
        terminated:
          exitCode: 0
          reason: Completed
          startedAt: '2023-09-15T11:34:05Z'
          finishedAt: '2023-09-15T11:34:05Z'
          containerID: containerd://98f8169020b2c32e8c5ecbf3d2275c120706d874df4d52a355b1b594e0e03950
      lastState: {}
      ready: true
      restartCount: 0
      image: cr.ttyuyin.com/devops/tekton/entrypoint:v0.46.0
      imageID: cr.ttyuyin.com/devops/tekton/entrypoint@sha256:0a4e875cb42ec35dfa34f46d99bd5e7b446c45490af7fe2b0733d39c13b6a1d5
      containerID: containerd://98f8169020b2c32e8c5ecbf3d2275c120706d874df4d52a355b1b594e0e03950
    - name: place-scripts
      state:
        terminated:
          exitCode: 0
          reason: Completed
          startedAt: '2023-09-15T11:34:06Z'
          finishedAt: '2023-09-15T11:34:06Z'
          containerID: containerd://19e35a3572df2a17f5b3e9e5c2c8211e6a7449d99f16649026b3c134301f0f7a
      lastState: {}
      ready: true
      restartCount: 0
      image: cr.ttyuyin.com/devops/busybox:latest
      imageID: cr.ttyuyin.com/devops/busybox@sha256:a4efb40c0070eeb1341378ee6ddff093a92c27fa3e2f44d4f188ef16fe2d092f
      containerID: containerd://19e35a3572df2a17f5b3e9e5c2c8211e6a7449d99f16649026b3c134301f0f7a
    - name: working-dir-initializer
      state:
        terminated:
          exitCode: 0
          reason: Completed
          startedAt: '2023-09-15T11:34:07Z'
          finishedAt: '2023-09-15T11:34:07Z'
          containerID: containerd://4bcbc5b18e304631658cfb6d06847a6fdc640e8632eb59bd6bbb02c850ac26e7
      lastState: {}
      ready: true
      restartCount: 0
      image: cr.ttyuyin.com/devops/tekton/workingdirinit:v0.46.0
      imageID: cr.ttyuyin.com/devops/tekton/workingdirinit@sha256:070897ebfac7f143585808ef0b8c7488d61bbce7d9c77b93e8830af4147925b5
      containerID: containerd://4bcbc5b18e304631658cfb6d06847a6fdc640e8632eb59bd6bbb02c850ac26e7
  containerStatuses:
    - name: step-build
      state:
        terminated:
          exitCode: 1
          reason: Error
          message: '[{"key":"StartedAt","value":"2023-09-15T11:34:11.265Z","type":3}]'
          startedAt: '2023-09-15T11:34:09Z'
          finishedAt: '2023-09-15T11:34:11Z'
          containerID: containerd://36b0a435ff57c49fbd3f54810449ce7a7738ac038b09b6996c0657e4d9ff0e79
      lastState: {}
      ready: false
      restartCount: 0
      image: docker.io/library/golang:latest
      imageID: docker.io/library/golang@sha256:62e5883c278354041360e26dd3ba8c62c8621743a2b4fd7f0f0caf00af45d15c
      containerID: containerd://36b0a435ff57c49fbd3f54810449ce7a7738ac038b09b6996c0657e4d9ff0e79
      started: false
  qosClass: BestEffort
