apiVersion: v1
kind: ServiceAccount
metadata:
  name: vela-dryrun
  namespace: qa
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: vela-dryrun-role
rules:
- apiGroups: ["core.oam.dev"]
  resources: ["*"]
  verbs: ["*"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: vela-dryrun-rolebinding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: vela-dryrun-role
subjects:
- kind: ServiceAccount
  name: vela-dryrun
  namespace: qa